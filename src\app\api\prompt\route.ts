/**
 * 提示词API路由
 * 处理用户提示词的查询请求
 */
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { createClient as createServerClient } from '@/utils/supabase/server';

// Supabase配置 - 使用anon key，因为函数使用SECURITY DEFINER
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://127.0.0.1:54321';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

// 创建Supabase客户端（使用anon key，函数有SECURITY DEFINER权限）
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// 硬编码密码用于访问get_prompt_content函数
const PROMPT_ACCESS_PASSWORD = 'PROMPT_ACCESS_2024_SECURE_KEY';

/**
 * 服务器端获取当前用户
 */
async function getCurrentUser(request: NextRequest) {
  try {
    // 从请求头获取Authorization token
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // 如果没有Authorization头，尝试使用cookies方式
      const supabaseServer = createServerClient(
        supabaseUrl,
        supabaseAnonKey,
        {
          cookies: {
            getAll() {
              return request.cookies.getAll().map(cookie => ({
                name: cookie.name,
                value: cookie.value
              }));
            },
            setAll() {
              // 在API路由中不需要设置cookies
            },
          },
        }
      );

      const { data: { user }, error } = await supabaseServer.auth.getUser();
      if (error) {
        console.error('获取用户失败:', error);
        return null;
      }
      return user;
    }

    // 使用Authorization token获取用户
    const token = authHeader.substring(7); // 移除 "Bearer " 前缀
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error) {
      console.error('获取用户失败:', error);
      return null;
    }

    return user;
  } catch (error) {
    console.error('获取用户异常:', error);
    return null;
  }
}

/**
 * 严格的UUID格式验证函数
 * 防止SQL注入和子查询攻击
 */
function validateUUID(input: string): boolean {
  // 检查基本格式
  if (!input || typeof input !== 'string') {
    return false;
  }

  // 检查长度（标准UUID是36个字符）
  if (input.length !== 36) {
    return false;
  }

  // 检查是否包含SQL关键字或特殊字符（防止注入攻击）
  const sqlKeywords = [
    'SELECT', 'FROM', 'WHERE', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END',
    'UNION', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'CREATE', 'ALTER',
    'EXEC', 'EXECUTE', 'DECLARE', 'CAST', 'CONVERT', 'SUBSTRING',
    '(', ')', ';', '--', '/*', '*/', '||', '&&'
  ];

  const upperInput = input.toUpperCase();
  for (const keyword of sqlKeywords) {
    if (upperInput.includes(keyword)) {
      return false;
    }
  }

  // 检查标准UUID格式：xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
  // 只允许十六进制字符和连字符
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(input)) {
    return false;
  }

  // 额外检查：确保连字符在正确位置
  const parts = input.split('-');
  if (parts.length !== 5) {
    return false;
  }

  if (parts[0].length !== 8 || parts[1].length !== 4 ||
      parts[2].length !== 4 || parts[3].length !== 4 ||
      parts[4].length !== 12) {
    return false;
  }

  return true;
}

/**
 * GET 方法 - 根据ID获取提示词内容
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const promptId = searchParams.get('id');

    if (!promptId) {
      return NextResponse.json(
        { error: '提示词ID不能为空' },
        { status: 400 }
      );
    }

    // 严格的UUID格式验证
    if (!validateUUID(promptId)) {
      console.error(`[Prompt API] 无效的UUID格式: ${promptId}`);
      return NextResponse.json(
        { error: '无效的UUID格式' },
        { status: 400 }
      );
    }

    // 获取当前用户
    const user = await getCurrentUser(request);
    const userEmail = user?.email || null;

    console.log(`[Prompt API] 查询提示词ID: ${promptId}, 用户: ${userEmail || '未登录'}`);

    // 调用数据库函数获取提示词内容（带密码校验和用户权限验证）
    const { data: result, error } = await supabase
      .rpc('get_prompt_content', {
        prompt_id_param: promptId,
        access_password: PROMPT_ACCESS_PASSWORD,
        user_email_param: userEmail
      });

    if (error) {
      console.error('[Prompt API] 调用数据库函数失败:', error);
      return NextResponse.json(
        { error: '查询提示词失败' },
        { status: 500 }
      );
    }

    // 检查函数返回的结果
    if (!result || !result.success) {
      const errorMsg = result?.error || '提示词不存在';
      const errorCode = result?.code || 'UNKNOWN_ERROR';

      console.error(`[Prompt API] ${errorMsg} (${errorCode})`);

      // 特殊处理各种错误
      if (errorCode === 'INVALID_PASSWORD') {
        return NextResponse.json(
          { error: '访问被拒绝' },
          { status: 403 }
        );
      }

      if (errorCode === 'PERMISSION_DENIED') {
        return NextResponse.json(
          { error: '无权限访问此提示词' },
          { status: 403 }
        );
      }

      const statusCode = errorCode === 'PROMPT_NOT_FOUND' || errorCode === 'CONTENT_NOT_FOUND' ? 404 : 500;
      return NextResponse.json(
        { error: errorMsg },
        { status: statusCode }
      );
    }

    console.log(`[Prompt API] 成功获取提示词: ${result.data.title}`);

    return NextResponse.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('[Prompt API] 服务器错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * 验证字符串参数，防止SQL注入
 */
function validateStringParam(input: any, maxLength: number = 50): boolean {
  if (input === null || input === undefined) {
    return true; // 允许空值
  }

  if (typeof input !== 'string') {
    return false;
  }

  if (input.length > maxLength) {
    return false;
  }

  // 检查是否包含SQL关键字或特殊字符
  const sqlKeywords = [
    'SELECT', 'FROM', 'WHERE', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END',
    'UNION', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'CREATE', 'ALTER',
    'EXEC', 'EXECUTE', 'DECLARE', 'CAST', 'CONVERT', 'SUBSTRING',
    '(', ')', ';', '--', '/*', '*/', '||', '&&', '<', '>', '='
  ];

  const upperInput = input.toUpperCase();
  for (const keyword of sqlKeywords) {
    if (upperInput.includes(keyword)) {
      return false;
    }
  }

  // 只允许字母、数字、下划线和连字符
  const allowedCharsRegex = /^[a-zA-Z0-9_-]+$/;
  return allowedCharsRegex.test(input);
}

/**
 * 验证数字参数
 */
function validateNumberParam(input: any, min: number = 1, max: number = 100): boolean {
  if (input === null || input === undefined) {
    return true; // 允许空值，会使用默认值
  }

  const num = Number(input);
  if (isNaN(num) || !Number.isInteger(num)) {
    return false;
  }

  return num >= min && num <= max;
}

/**
 * POST 方法 - 批量获取提示词展示信息
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { category, type, limit = 50 } = body;

    // 验证参数
    if (!validateStringParam(category, 20)) {
      console.error(`[Prompt API] 无效的category参数: ${category}`);
      return NextResponse.json(
        { error: '无效的分类参数' },
        { status: 400 }
      );
    }

    if (!validateStringParam(type, 30)) {
      console.error(`[Prompt API] 无效的type参数: ${type}`);
      return NextResponse.json(
        { error: '无效的类型参数' },
        { status: 400 }
      );
    }

    if (!validateNumberParam(limit, 1, 100)) {
      console.error(`[Prompt API] 无效的limit参数: ${limit}`);
      return NextResponse.json(
        { error: '无效的限制参数，必须是1-100之间的整数' },
        { status: 400 }
      );
    }

    console.log(`[Prompt API] 查询提示词列表 - 分类: ${category}, 类型: ${type}`);

    // 使用Supabase.js直接查询提示词列表，包含使用次数
    let query = supabase
      .from('propmt-zhanshi')
      .select('id, title, description, category, type, created_at, updated_at, created_by, author_display_id, usage_count')
      .order('updated_at', { ascending: false })
      .limit(limit);

    if (category) {
      query = query.eq('category', category);
    }
    if (type) {
      query = query.eq('type', type);
    }

    const { data: prompts, error } = await query;

    if (error) {
      console.error('[Prompt API] 查询提示词列表失败:', error);
      return NextResponse.json(
        { error: '查询提示词列表失败' },
        { status: 500 }
      );
    }
    console.log(`[Prompt API] 成功获取 ${prompts?.length || 0} 个提示词`);

    return NextResponse.json({
      success: true,
      data: prompts || []
    });

  } catch (error) {
    console.error('[Prompt API] 服务器错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * PUT 方法 - 创建或更新用户提示词
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, title, description, type, content } = body;

    // 获取当前用户
    const user = await getCurrentUser(request);
    if (!user || !user.email) {
      return NextResponse.json(
        { error: '用户未登录' },
        { status: 401 }
      );
    }

    // 参数验证
    if (!title || typeof title !== 'string' || title.trim().length === 0) {
      return NextResponse.json(
        { error: '提示词标题不能为空' },
        { status: 400 }
      );
    }

    if (!type || typeof type !== 'string' || type.trim().length === 0) {
      return NextResponse.json(
        { error: '提示词类型不能为空' },
        { status: 400 }
      );
    }

    if (!content || typeof content !== 'string' || content.trim().length === 0) {
      return NextResponse.json(
        { error: '提示词内容不能为空' },
        { status: 400 }
      );
    }

    // 内容长度限制（一万字）
    if (content.length > 10000) {
      return NextResponse.json(
        { error: '提示词内容不能超过10000字' },
        { status: 400 }
      );
    }

    // 验证类型是否有效
    const validTypes = ['ai_writing', 'ai_polishing', 'character', 'worldbuilding'];
    if (!validTypes.includes(type)) {
      return NextResponse.json(
        { error: '无效的提示词类型' },
        { status: 400 }
      );
    }

    let result;

    if (id) {
      // 更新现有提示词
      if (!validateUUID(id)) {
        return NextResponse.json(
          { error: '无效的提示词ID格式' },
          { status: 400 }
        );
      }

      console.log(`[Prompt API] 更新提示词ID: ${id}`);

      const { data: updateResult, error } = await supabase
        .rpc('update_user_prompt', {
          prompt_id_param: id,
          title_param: title.trim(),
          description_param: description ? description.trim() : '',
          type_param: type.trim(),
          content_param: content.trim(),
          user_email_param: user.email,
          access_password: PROMPT_ACCESS_PASSWORD
        });

      if (error) {
        console.error('[Prompt API] 更新提示词失败:', error);
        return NextResponse.json(
          { error: '更新提示词失败' },
          { status: 500 }
        );
      }

      result = updateResult;
    } else {
      // 创建新提示词
      console.log(`[Prompt API] 创建新提示词: ${title}`);

      // 获取用户的display_name
      const userDisplayName = user.user_metadata?.display_name || user.user_metadata?.name || user.email?.split('@')[0] || '未知用户';

      const { data: createResult, error } = await supabase
        .rpc('create_user_prompt', {
          title_param: title.trim(),
          description_param: description ? description.trim() : '',
          type_param: type.trim(),
          content_param: content.trim(),
          user_email_param: user.email,
          user_display_name_param: userDisplayName,
          access_password: PROMPT_ACCESS_PASSWORD
        });

      if (error) {
        console.error('[Prompt API] 创建提示词失败:', error);
        return NextResponse.json(
          { error: '创建提示词失败' },
          { status: 500 }
        );
      }

      result = createResult;
    }

    // 检查函数返回的结果
    if (!result || !result.success) {
      const errorMsg = result?.error || '操作失败';
      const errorCode = result?.code || 'UNKNOWN_ERROR';

      console.error(`[Prompt API] ${errorMsg} (${errorCode})`);

      // 根据错误代码返回相应的HTTP状态码
      let statusCode = 500;
      if (errorCode === 'INVALID_PASSWORD') {
        statusCode = 403;
      } else if (errorCode === 'PROMPT_NOT_FOUND') {
        statusCode = 404;
      } else if (errorCode === 'PERMISSION_DENIED') {
        statusCode = 403;
      } else if (errorCode.includes('INVALID_') || errorCode === 'CONTENT_TOO_LONG') {
        statusCode = 400;
      }

      return NextResponse.json(
        { error: errorMsg },
        { status: statusCode }
      );
    }

    console.log(`[Prompt API] 成功${id ? '更新' : '创建'}提示词: ${result.data.title}`);

    return NextResponse.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('[Prompt API] 服务器错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * DELETE 方法 - 删除用户提示词
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const promptId = searchParams.get('id');

    if (!promptId) {
      return NextResponse.json(
        { error: '提示词ID不能为空' },
        { status: 400 }
      );
    }

    // 严格的UUID格式验证
    if (!validateUUID(promptId)) {
      console.error(`[Prompt API] 无效的UUID格式: ${promptId}`);
      return NextResponse.json(
        { error: '无效的UUID格式' },
        { status: 400 }
      );
    }

    // 获取当前用户
    const user = await getCurrentUser(request);
    if (!user || !user.email) {
      return NextResponse.json(
        { error: '用户未登录' },
        { status: 401 }
      );
    }

    console.log(`[Prompt API] 删除提示词ID: ${promptId}`);

    // 调用数据库函数删除提示词
    const { data: result, error } = await supabase
      .rpc('delete_user_prompt', {
        prompt_id_param: promptId,
        user_email_param: user.email,
        access_password: PROMPT_ACCESS_PASSWORD
      });

    if (error) {
      console.error('[Prompt API] 调用删除函数失败:', error);
      return NextResponse.json(
        { error: '删除提示词失败' },
        { status: 500 }
      );
    }

    // 检查函数返回的结果
    if (!result || !result.success) {
      const errorMsg = result?.error || '删除失败';
      const errorCode = result?.code || 'UNKNOWN_ERROR';

      console.error(`[Prompt API] ${errorMsg} (${errorCode})`);

      // 根据错误代码返回相应的HTTP状态码
      let statusCode = 500;
      if (errorCode === 'INVALID_PASSWORD') {
        statusCode = 403;
      } else if (errorCode === 'PROMPT_NOT_FOUND') {
        statusCode = 404;
      } else if (errorCode === 'PERMISSION_DENIED') {
        statusCode = 403;
      } else if (errorCode.includes('INVALID_')) {
        statusCode = 400;
      }

      return NextResponse.json(
        { error: errorMsg },
        { status: statusCode }
      );
    }

    console.log(`[Prompt API] 成功删除提示词: ${result.data.title}`);

    return NextResponse.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('[Prompt API] 服务器错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * PATCH 方法 - 获取提示词使用统计
 */
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, prompt_id } = body;

    if (action === 'get_usage_stats') {
      console.log(`[Prompt API] 获取使用统计 - 提示词ID: ${prompt_id || '全部'}`);

      // 调用数据库函数获取使用统计
      const { data: result, error } = await supabase
        .rpc('get_prompt_usage_stats', {
          prompt_id_param: prompt_id || null,
          access_password: PROMPT_ACCESS_PASSWORD
        });

      if (error) {
        console.error('[Prompt API] 获取使用统计失败:', error);
        return NextResponse.json(
          { error: '获取使用统计失败' },
          { status: 500 }
        );
      }

      if (!result || !result.success) {
        const errorMsg = result?.error || '获取统计失败';
        const errorCode = result?.code || 'UNKNOWN_ERROR';
        console.error(`[Prompt API] ${errorMsg} (${errorCode})`);

        const statusCode = errorCode === 'PROMPT_NOT_FOUND' ? 404 : 500;
        return NextResponse.json(
          { error: errorMsg },
          { status: statusCode }
        );
      }

      console.log(`[Prompt API] 成功获取使用统计`);
      return NextResponse.json({
        success: true,
        data: result.data
      });
    }

    return NextResponse.json(
      { error: '不支持的操作' },
      { status: 400 }
    );

  } catch (error) {
    console.error('[Prompt API] 服务器错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
